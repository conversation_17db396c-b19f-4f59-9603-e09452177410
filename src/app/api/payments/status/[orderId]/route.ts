import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/server/auth'
import { paymentService } from '@/lib/payments/payment-service'
import { momoProvider } from '@/lib/payments/providers/momo'
import { zaloPayProvider } from '@/lib/payments/providers/zalopay'
import { vnPayProvider } from '@/lib/payments/providers/vnpay'
import { stripeProvider } from '@/lib/payments/providers/stripe'
import { paypalProvider } from '@/lib/payments/providers/paypal'
import { db } from '@/server/db'
import { PaymentStatus, PaymentEventType } from '@prisma/client'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { orderId } = await params

    // Get payment from database
    const payment = await paymentService.getPaymentByOrderId(orderId)

    if (!payment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      )
    }

    // Check if user owns the payment
    if (payment.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      )
    }

    // If payment is already completed or failed, return current status
    if (payment.status === PaymentStatus.COMPLETED || 
        payment.status === PaymentStatus.FAILED ||
        payment.status === PaymentStatus.CANCELLED ||
        payment.status === PaymentStatus.REFUNDED) {
      return NextResponse.json({
        success: true,
        data: {
          orderId: payment.orderId,
          status: payment.status,
          amount: payment.amount,
          currency: payment.currency,
          paymentMethod: payment.paymentMethod,
          paidAt: payment.paidAt,
          externalId: payment.externalId,
          events: payment.events,
        },
      })
    }

    // Query payment status from provider
    let providerStatus: any = null
    try {
      switch (payment.provider) {
        case 'momo':
          providerStatus = await momoProvider.queryPaymentStatus(orderId)
          break
        case 'zalopay':
          const appTransId = payment.providerData?.app_trans_id || 
                           `${new Date().toISOString().slice(2, 10).replace(/-/g, '')}_${orderId}`
          providerStatus = await zaloPayProvider.queryPaymentStatus(appTransId)
          break
        case 'vnpay':
          if (payment.providerData?.vnp_CreateDate) {
            providerStatus = await vnPayProvider.queryPaymentStatus({
              orderId,
              transactionDate: payment.providerData.vnp_CreateDate,
            })
          }
          break
        case 'stripe':
          if (payment.externalId) {
            providerStatus = await stripeProvider.retrievePaymentIntent(payment.externalId)
          }
          break
        case 'paypal':
          if (payment.externalId) {
            providerStatus = await paypalProvider.getOrderDetails(payment.externalId)
          }
          break
      }
    } catch (error) {
      console.error(`Failed to query ${payment.provider} status:`, error)
    }

    // Update payment status if provider returned new information
    let updatedPayment = payment
    if (providerStatus) {
      const statusUpdate = processProviderStatus(payment.provider, providerStatus)
      
      if (statusUpdate && statusUpdate.status !== payment.status) {
        updatedPayment = await db.payment.update({
          where: { id: payment.id },
          data: {
            status: statusUpdate.status,
            paidAt: statusUpdate.paidAt,
            errorMessage: statusUpdate.errorMessage,
            providerData: {
              ...payment.providerData,
              latestQuery: providerStatus,
            },
            updatedAt: new Date(),
          },
          include: {
            events: {
              orderBy: { createdAt: 'desc' },
            },
          },
        })

        // Create payment event
        await db.paymentEvent.create({
          data: {
            paymentId: payment.id,
            eventType: PaymentEventType.WEBHOOK,
            status: statusUpdate.status,
            message: `Status updated from provider query: ${statusUpdate.message}`,
            data: providerStatus,
          },
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        orderId: updatedPayment.orderId,
        status: updatedPayment.status,
        amount: updatedPayment.amount,
        currency: updatedPayment.currency,
        paymentMethod: updatedPayment.paymentMethod,
        paidAt: updatedPayment.paidAt,
        externalId: updatedPayment.externalId,
        events: updatedPayment.events,
        providerStatus,
      },
    })

  } catch (error) {
    console.error('Payment status API error:', error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    )
  }
}

function processProviderStatus(provider: string, providerStatus: any): {
  status: PaymentStatus
  paidAt?: Date
  errorMessage?: string
  message: string
} | null {
  switch (provider) {
    case 'momo':
      if (providerStatus.resultCode === 0) {
        return {
          status: PaymentStatus.COMPLETED,
          paidAt: new Date(),
          message: 'Payment completed successfully',
        }
      } else if (providerStatus.resultCode === 1000) {
        return {
          status: PaymentStatus.FAILED,
          errorMessage: 'Payment failed',
          message: 'Payment failed',
        }
      }
      break

    case 'zalopay':
      if (providerStatus.return_code === 1) {
        return {
          status: PaymentStatus.COMPLETED,
          paidAt: new Date(),
          message: 'Payment completed successfully',
        }
      } else if (providerStatus.return_code === 2) {
        return {
          status: PaymentStatus.FAILED,
          errorMessage: 'Payment failed',
          message: 'Payment failed',
        }
      }
      break

    case 'vnpay':
      if (providerStatus.vnp_ResponseCode === '00') {
        return {
          status: PaymentStatus.COMPLETED,
          paidAt: new Date(),
          message: 'Payment completed successfully',
        }
      } else if (providerStatus.vnp_ResponseCode !== '02') { // 02 = pending
        return {
          status: PaymentStatus.FAILED,
          errorMessage: `Payment failed: ${providerStatus.vnp_ResponseCode}`,
          message: 'Payment failed',
        }
      }
      break

    case 'stripe':
      if (providerStatus.status === 'succeeded') {
        return {
          status: PaymentStatus.COMPLETED,
          paidAt: new Date(),
          message: 'Payment completed successfully',
        }
      } else if (providerStatus.status === 'canceled') {
        return {
          status: PaymentStatus.CANCELLED,
          message: 'Payment was cancelled',
        }
      } else if (providerStatus.status === 'requires_payment_method') {
        return {
          status: PaymentStatus.FAILED,
          errorMessage: 'Payment method required',
          message: 'Payment failed',
        }
      }
      break

    case 'paypal':
      if (providerStatus.status === 'COMPLETED') {
        return {
          status: PaymentStatus.COMPLETED,
          paidAt: new Date(),
          message: 'Payment completed successfully',
        }
      } else if (providerStatus.status === 'VOIDED') {
        return {
          status: PaymentStatus.CANCELLED,
          message: 'Payment was cancelled',
        }
      }
      break
  }

  return null
}
