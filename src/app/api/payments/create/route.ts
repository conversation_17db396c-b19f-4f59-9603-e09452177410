import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/server/auth'
import { paymentService } from '@/lib/payments/payment-service'
import { PaymentMethod } from '@prisma/client'
import { z } from 'zod'

const createPaymentSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().min(3).max(3, 'Currency must be 3 characters'),
  paymentMethod: z.nativeEnum(PaymentMethod),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  returnUrl: z.string().url().optional(),
  cancelUrl: z.string().url().optional(),
})

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = createPaymentSchema.parse(body)

    // Create payment
    const payment = await paymentService.createPayment({
      userId: session.user.id,
      ...validatedData,
    })

    return NextResponse.json({
      success: true,
      data: payment,
    })

  } catch (error) {
    console.error('Payment creation API error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get user payments
    const payments = await paymentService.getUserPayments(
      session.user.id,
      limit,
      offset
    )

    return NextResponse.json({
      success: true,
      data: payments,
    })

  } catch (error) {
    console.error('Get payments API error:', error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    )
  }
}
